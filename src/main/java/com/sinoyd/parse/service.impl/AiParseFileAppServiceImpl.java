package com.sinoyd.parse.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.enums.EnumAIParseStatus;
import com.sinoyd.parse.enums.EnumDocType;
import com.sinoyd.parse.enums.EnumParseType;
import com.sinoyd.parse.enums.EnumWebSocketType;
import com.sinoyd.parse.repository.AiInstrumentConfigRepository;
import com.sinoyd.parse.repository.AiParseFileAppRepository;
import com.sinoyd.parse.repository.ParseDocumentRepository;
import com.sinoyd.parse.service.AiParseFileAppService;
import com.sinoyd.parse.vo.AiParseBatchVO;
import com.sinoyd.parse.websocket.AiInstrumentParseWSServer;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AI仪器解析应用操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Slf4j
@Service
public class AiParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoAiParseFileApp, String, AiParseFileAppRepository> implements AiParseFileAppService {

    @Value("${ai-parse.url:http://localhost:8866/predict/ocr_system}")
    private String ocrUrl;

    @Value("${ai-parse.default-prompt:角色：你是位资深的光学数据识别专家,任务：你需要识别图片内容并将内容按照指定的json格式输出,要求,1.禁止将中文转成英文,禁止将单位转成中文,2.数据中的*需要去掉,3.数据不需要单位,4.采样日期和采样时间不需要分开。指定的json格式示例如下{参数名称1:参数值1,参数名称2:参数值2},去掉不必要的换行符,生成的json文本要能支持直接转换为java中的JSONObject,回答不需要其余说明,只需要输出json文本。}")
    private String ocrPrompt;



    private AiInstrumentConfigRepository aiInstrumentConfigRepository;

    private ParseDocumentRepository parseDocumentRepository;

    private RedisTemplate redisTemplate;


    @Override
    public void findByPage(PageBean<DtoAiParseFileApp> pb, BaseCriteria aiParseFileAppCriteria) {
        pb.setEntityName("DtoAiParseFileApp a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, aiParseFileAppCriteria);

        // 设置解析方式名称和解析状态名称
        List<DtoAiParseFileApp> appList = pb.getData();
        loadTransientFields(appList);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp save(DtoAiParseFileApp entity) {
        //设置解析状态为未解析
        entity.setParseStatus(EnumAIParseStatus.UN_PARS.getValue());
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp update(DtoAiParseFileApp entity) {
        return super.update(entity);
    }

    @Override
    public DtoAiParseFileApp findOne(String key) {
        DtoAiParseFileApp fileApp = super.findOne(key);
        loadTransientFields(Collections.singletonList(fileApp));
        return fileApp;
    }

    @Override
    public void batchParse(AiParseBatchVO parseBatchVO) {
        if (StringUtil.isEmpty(parseBatchVO.getAppIds())) {
            return;
        }
        //SessionId
        String sessionId = parseBatchVO.getSessionId();
        // 固定3线程并发执行
        ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(3);
        List<CompletableFuture<Void>> tasks = new java.util.ArrayList<>();

        for (String appId : parseBatchVO.getAppIds()) {
            CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                if (appId.equals(parseBatchVO.getCurrentAppId())) {
                    // 1. 开始
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "解析步骤1..." + appId);
                    // 2. 预处理（示例逻辑，可替换为OCR/文本处理）
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "解析步骤2..." + appId);
                    // 3. AI解析（占位，可对接流式AI接口并逐行 onData.accept(json)）
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "解析步骤3..." + appId);
                    // 4. 结束
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "解析步骤4..." + appId);
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "解析完成..." + appId);
                } else {
                    // 1. 开始
                    // 2. 预处理（示例逻辑，可替换为OCR/文本处理）
                    // 3. AI解析（占位，可对接流式AI接口并逐行 onData.accept(json)）
                    // 4. 结束
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "解析完成..." + appId);
                }
            }, executor);
            tasks.add(task);
        }

        try {
            CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
        } finally {
            AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "数据全部解析完成...");
//            AiInstrumentParseWSServer.closeConnection(sessionId);
        }
    }

    @Override
    public DtoAiParseFileApp findAttachPath(String id) {
        return repository.findOne(id);
    }


    /**
     * AI解析请求（向WebSocket发送步骤消息）
     *
     * @param url      AI解析请求地址
     * @param prompt   AI解析提示词
     * @param filePath AI解析图片文件路径
     * @return 是否请求成功
     */
    public Boolean sendOcrRequestMsg(AiParseBatchVO parseBatchVO,  String url, String prompt, String filePath) {
        String requestUrl = StringUtil.isNotEmpty(ocrUrl) ? ocrUrl : url;
        File file = new File(filePath);
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("prompt", StringUtil.isNotEmpty(prompt) ? prompt : ocrPrompt)
                .addFormDataPart("image_file", file.getName(),
                        RequestBody.create(MediaType.parse("application/octet-stream"), file))
                .build();
        Request request = new Request.Builder()
                .url(ocrUrl)
                .post(body)
                .build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new RuntimeException("API call failed: " + response.code());
            }
            try (ResponseBody responseBody = response.body();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(),
                         StandardCharsets.UTF_8))) {
                String line;
                Integer step = 0;
                while ((line = reader.readLine()) != null) {
                    step++;
                    if (!line.trim().isEmpty() && !line.contains("id:")) {
                        String json = line.replace("data: ", "");
                        try {
                            Boolean done = JSONObject.parseObject(json).getBoolean("done");
                            if (!done) {
                                JSONObject originObject = JSONObject.parseObject(json).getJSONObject("workflow_result");
                                container.setAiAnswer(originObject.toJSONString());
                                container.setRecordId(ocrConfigRecord.getId());
                                //数据转换
                                parseOriginData(ocrConfigRecord, initDatalist, paramList, container);
                                //初始数据保存
                                ocrConfigRecordRepository.save(ocrConfigRecord);
                                ocrConfigParamDataRepository.save(initDatalist);
                                //存储容器
                                container.setDataList(initDatalist);
                                String endStr = JsonUtil.toJson(container);
                                // 立即处理每一行数据，不等待所有数据读取完成
                                onData.accept(endStr);
                            } else {
                                // 如果不是解析完成则正常发布步骤数据
                                AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, json);
                                //将AI步骤数据保存到解析日志中（先存入缓存，当全部数据解析完成之后，持久化到数据库中）
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            throw new BaseException("AI识别异常");
                        }
                        // 添加小延迟，确保数据能够及时发送
                        try {
                            Thread.sleep(10);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Stream API call failed", e);
            throw new RuntimeException("Stream API call failed", e);
        }
        return null;
    }


    /**
     * 加载冗余字段
     *
     * @param data 应用数据
     */
    private void loadTransientFields(Collection<DtoAiParseFileApp> data) {

        //查询附件数据
        List<String> appIds = data.stream().map(DtoAiParseFileApp::getId).collect(Collectors.toList());
        List<DtoParseDocument> documentList = parseDocumentRepository.findByObjectIdInAndDocTypeId(appIds, EnumDocType.AI_PARSE_APP_FILE.getDocTypeId());
        //按照ObjectId分组附件数据
        Map<String, List<DtoParseDocument>> documentMap = documentList.stream().collect(Collectors.groupingBy(DtoParseDocument::getObjectId));
        //相关枚举Map
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        Map<String, String> parseStatusMap = EnumAIParseStatus.getMapData();

        for (DtoAiParseFileApp app : data) {
            //获取应用下的最新的附件
            List<DtoParseDocument> documents = documentMap.getOrDefault(app.getId(), null);
            if (documents != null && !documents.isEmpty()) {
                Optional<DtoParseDocument> docOp = documents.stream().max(Comparator.comparing(DtoParseDocument::getCreateDate));
                docOp.ifPresent(p -> {
                    app.setDocumentId(p.getId());
                    app.setDocumentPath(p.getPath());
                });

            }
            // 设置解析方式名称
            if (app.getParseType() != null) {
                String parseTypeName = parseTypeMap.getOrDefault(app.getParseType(), "");
                app.setParseTypeName(parseTypeName);
            }

            // 设置解析状态名称
            if (app.getParseStatus() != null) {
                String parseStatusName = parseStatusMap.getOrDefault(app.getParseStatus(), "");
                app.setParseStatusName(parseStatusName);
            }
        }
    }

    @Autowired
    public void setAiInstrumentConfigRepository(AiInstrumentConfigRepository aiInstrumentConfigRepository) {
        this.aiInstrumentConfigRepository = aiInstrumentConfigRepository;
    }

    @Autowired
    public void setParseDocumentRepository(ParseDocumentRepository parseDocumentRepository) {
        this.parseDocumentRepository = parseDocumentRepository;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
